import { useState, useEffect, useRef, useCallback } from "react";
import AgoraChat from "agora-chat";
import {AuthService} from "../services/AuthService";

const AGORA_APP_KEY = "611215916#1407081";

const useChat = (userId, token) => {
  const [connectionStatus, setConnectionStatus] = useState("disconnected");
  const [messages, setMessages] = useState({});
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [typingUsers, setTypingUsers] = useState(new Set());
  const [unreadCounts, setUnreadCounts] = useState({});
  const [missedCallCounts, setMissedCallCounts] = useState({});
  const [error, setError] = useState(null);

  const chatClientRef = useRef(null);
  const isMounted = useRef(true);
  const presenceSubscribedUsers = useRef(new Set());
  const pendingMessageIdsRef = useRef(new Map());
  const processedMissedCallsRef = useRef(new Map());

  // Initialize chat service
  useEffect(() => {
    chatClientRef.current = new AgoraChat.connection({
      appKey: AGORA_APP_KEY,
    });

    chatClientRef.current.addEventHandler("connection&message", {
      onConnected: () => {
        setConnectionStatus("connected");
        pendingMessageIdsRef.current = new Map();
      },

      onDisconnected: () => {
        setConnectionStatus("disconnected");
      },

      onReconnecting: () => {
        setConnectionStatus("reconnecting");
      },

      onOffline: () => {
        setConnectionStatus("offline");
      },

      onOnline: () => {
        // Only try reconnecting if we were previously connected
        if (
          connectionStatus === "offline" ||
          connectionStatus === "disconnected"
        ) {
          connectToChat();
        }
      },

      onTextMessage: (message) => {
        handleNewMessage(message);
      },

      onCmdMessage: (message) => {
        if (message.action === "typing") {
          handleTypingIndicator(message);
        } else if (message.action === "call_activity" && message.ext?.callActivity) {
          // Handle incoming call activity notifications
          const callActivity = message.ext.callActivity;

          console.log(`Received call activity message from ${message.from}:`, callActivity.type);

          // Important! Call activity needs to be interpreted from the recipient's perspective
          const isFromMe = message.from === userId;
          const isOutgoing = isFromMe ? callActivity.isOutgoing : !callActivity.isOutgoing;

          // Only show important call activities: outgoing/incoming calls and missed calls
          // Skip other activity types
          let displayText = "";
          let shouldDisplayMessage = true;

          // Check if this is a video call
          const isVideoCall = callActivity.isVideoCall === true;
          const callIcon = isVideoCall ? "🎥" : "📞";

          switch(callActivity.type) {
            case "call_started":
              displayText = isOutgoing
                ? `${callIcon} Outgoing ${isVideoCall ? 'video' : 'voice'} call`
                : `${callIcon} Incoming ${isVideoCall ? 'video' : 'voice'} call`;
              break;
            case "call_missed":
              if (isOutgoing) {
                displayText = `${callIcon} ${isVideoCall ? 'Video' : 'Voice'} call not answered`;
              } else {
                displayText = `${callIcon} Missed ${isVideoCall ? 'video' : 'voice'} call`;

                // Create a unique key for this missed call
                const callId = callActivity.callId;
                const peerId = message.from;

                // Initialize set for this conversation if needed
                if (!processedMissedCallsRef.current.has(peerId)) {
                  processedMissedCallsRef.current.set(peerId, new Set());
                }

                // Get the set of processed call IDs for this conversation
                const processedCallIds = processedMissedCallsRef.current.get(peerId);

                // Check if we've already processed this missed call
                if (!processedCallIds.has(callId)) {
                  // Track that we've processed this missed call
                  processedCallIds.add(callId);

                  // Increment missed call count only for incoming calls that I missed
                  if (isFromMe) {
                    setMissedCallCounts((prev) => ({
                      ...prev,
                      [peerId]: (prev[peerId] || 0) + 1,
                    }));
                  }
                } else {
                  // If we've already seen this missed call, don't show it again
                  console.log(`Ignoring duplicate missed call notification: ${peerId}_${callId}`);
                  shouldDisplayMessage = false;
                }
              }
              break;
            // For now, don't display these call activities
            case "call_ended":
            case "call_rejected":
            case "call_answered":
            default:
              shouldDisplayMessage = false;
              break;
          }

          // Only add system message for selected call activities
          if (shouldDisplayMessage) {
            // Create a system message to display in the chat
            const systemMessage = {
              id: `call_activity_${message.id || Date.now()}`,
              text: displayText,
              time: formatTime(new Date(message.time || Date.now())),
              timestamp: message.time || Date.now(),
              sent: isFromMe,
              from: message.from,
              to: message.to,
              status: "received",
              isSystemMessage: true,
              callActivity: {
                ...callActivity,
                // Update isOutgoing to match the perspective of the viewer
                isOutgoing: isOutgoing
              },
            };

            // Add to messages state
            setMessages((prev) => {
              const conversationId = isFromMe ? message.to : message.from;
              const existingMessages = prev[conversationId] || [];
              return {
                ...prev,
                [conversationId]: [...existingMessages, systemMessage],
              };
            });
          }
        }
      },

      onReceivedMessage: (message) => {
        updateDeliveredMessageStatus(
          message.id,
          message.mid,
          message.to,
          "delivered"
        );
      },

      // Update the presence handler
      onPresenceStatusChange: (presenceInfo) => {
        const currentOnlineUsers = new Set([...onlineUsers]);
        let hasChanges = false;

        const processPresence = (info) => {
          if (info.userId) {
            const isOnline = info.ext === "online"; // <- check ext field

            if (isOnline) {
              if (!currentOnlineUsers.has(info.userId)) {
                currentOnlineUsers.add(info.userId);
                hasChanges = true;
              }
            } else if (currentOnlineUsers.has(info.userId)) {
              currentOnlineUsers.delete(info.userId);
              hasChanges = true;
            }
          }
        };

        if (Array.isArray(presenceInfo)) {
          presenceInfo.forEach(processPresence);
        } else if (presenceInfo && presenceInfo.userId) {
          processPresence(presenceInfo);
        }

        if (hasChanges) {
          setOnlineUsers(currentOnlineUsers);
        }
      },

      onError: async (error) => {
        console.error("Chat error:", error);

        if (error?.type === 206) {
          console.warn(
            "User already logged in on another device. Reconnecting..."
          );

          // Safe reconnection
          (async () => {
            try {
              if (chatClientRef.current) {
                setConnectionStatus("connected");
              }
            } catch (e) {
              console.error("Error while closing chat client:", e);
            } finally {
              setConnectionStatus("connected");
            }
          })();

          return;
        }

        if (error?.type === 28) {
          console.warn("token not assign error");

          return;
        }

        if(error?.type === 56) {
          try {
            const agoraId = userId;
            const newTokens = await AuthService.refreshToken(agoraId);

            if (newTokens?.rtmToken) {
              await chatClientRef.current.renewToken(newTokens.rtmToken);
            }
          } catch (error) {
            console.error("Token renewal failed after expiration:", error);
            setConnectionStatus("disconnected");
            setError("Authentication token expired. Please log in again.");
          }

          return
        }

        setError(error.message || "An error occurred with the chat service");
        setConnectionStatus("error");
      },

      // Token expiration handlers
      onTokenWillExpire: async () => {
        console.warn("Token will expire soon");
        try {
          const agoraId = userId;

          if (!agoraId) throw new Error("User Agora ID missing");

          const newTokens = await AuthService.refreshToken(agoraId);

          if (newTokens?.rtmToken) {
            console.log("Agora token refreshed, renewing session...");
            await chatClientRef.current.renewToken(newTokens.rtmToken);
          }
        } catch (error) {
          console.error("Error refreshing Agora token:", error);
        }
      },

      onTokenExpired: async () => {
        try {
          const agoraId = userId;
          const newTokens = await AuthService.refreshToken(agoraId);

          if (newTokens?.rtmToken) {
            await chatClientRef.current.renewToken(newTokens.rtmToken);
          }
        } catch (error) {
          console.error("Token renewal failed after expiration:", error);
          setConnectionStatus("disconnected");
          setError("Authentication token expired. Please log in again.");
        }
      },
    });

    chatClientRef.current.addEventHandler("readReceiptHandler", {
      onReadMessage: (message) => {
        updateMessageStatus(message.id, "read");
      },
    });

    if (userId && token) {
      connectToChat();
    }

    return () => {
      isMounted.current = false;

      if (chatClientRef.current) {
        chatClientRef.current.removeEventHandler("connection&message");

        if (connectionStatus === "connected") {
          if (presenceSubscribedUsers.current.size > 0) {
            const userArray = Array.from(presenceSubscribedUsers.current);
            chatClientRef.current
              .unsubscribePresence({ usernames: userArray })
              .catch(console.error);
          }

          chatClientRef.current.close().catch(console.error);
        }
      }
    };
  }, [userId, token]);

  // Handle new message
  const handleNewMessage = (message) => {
    const conversationId = message.from === userId ? message.to : message.from;

    const formattedMessage = {
      id: message.id,
      text: message.msg,
      time: formatTime(new Date(message.time)),
      timestamp: message.time || Date.now(),
      sent: message.from === userId,
      status: "sent",
      from: message.from,
      to: message.to,
    };

    setMessages((prev) => {
      const existingMessages = prev[conversationId] || [];
      const messageExists = existingMessages.some(
        (msg) => msg.id === message.id
      );

      if (messageExists) {
        return prev;
      }

      return {
        ...prev,
        [conversationId]: [...existingMessages, formattedMessage],
      };
    });

    if (message.from !== userId) {
      setUnreadCounts((prev) => ({
        ...prev,
        [message.from]: (prev[message.from] || 0) + 1,
      }));
    }
  };

  // Handle typing indicator
  const handleTypingIndicator = (message) => {
    const senderId = message.from;

    if (message.ext?.status === "typing") {
      setTypingUsers((prev) => {
        const newSet = new Set(prev);
        newSet.add(senderId);
        return newSet;
      });

      setTimeout(() => {
        setTypingUsers((prev) => {
          const updated = new Set(prev);
          updated.delete(senderId);
          return updated;
        });
      }, 3000);
    } else {
      setTypingUsers((prev) => {
        const updated = new Set(prev);
        updated.delete(senderId);
        return updated;
      });
    }
  };

  // Update message status
  const updateMessageStatus = (messageId, status) => {
    if (!messageId) {
      console.warn(
        "Attempted to update message status with undefined messageId"
      );
      return;
    }

    setMessages((prev) => {
      const updated = { ...prev };
      let hasChanges = false;

      Object.keys(updated).forEach((conversationId) => {
        const updatedMessages = updated[conversationId].map((msg) => {
          if (msg.id === messageId && msg.status !== status) {
            hasChanges = true;
            return { ...msg, status };
          }
          return msg;
        });

        if (hasChanges) {
          updated[conversationId] = updatedMessages;
        }
      });

      return hasChanges ? updated : prev;
    });
  };

  // Update delivered message status
  const updateDeliveredMessageStatus = (serverId, newMessageId, to, status) => {
    if (!serverId || !newMessageId || !to) {
      console.warn("Missing required params", { serverId, newMessageId, to });
      return;
    }

    const tempIds = [];
    pendingMessageIdsRef.current.forEach((sId, tId) => {
      if (sId === serverId) {
        tempIds.push(tId);
      }
    });

    setMessages((prev) => {
      const conversationMessages = prev[to] || [];
      let hasChanges = false;

      const updatedMessages = conversationMessages.map((msg) => {
        const directMatch =
          msg.id === serverId ||
          msg.serverId === serverId ||
          msg.id === newMessageId;

        const tempMatch = tempIds.includes(msg.id);

        if (directMatch || tempMatch) {
          if (msg.status !== status) {
            hasChanges = true;

            if (tempMatch) {
              pendingMessageIdsRef.current.delete(msg.id);
            }

            return {
              ...msg,
              id: newMessageId,
              serverId: serverId,
              status,
            };
          }
        }
        return msg;
      });

      return hasChanges ? { ...prev, [to]: updatedMessages } : prev;
    });
  };

  // Mark the entier conversation
  const markConversationAsRead = async (conversationId) => {
    try {
      await chatClientRef.current.addConversationMark({
        conversations: [
          {
            conversationId,
            conversationType: "singleChat",
          },
        ],
        mark: 0, // 0 = read
      });

      // Reset unread count for this conversation
      setUnreadCounts((prev) => ({
        ...prev,
        [conversationId]: 0,
      }));

      // Also reset missed call count
      setMissedCallCounts((prev) => ({
        ...prev,
        [conversationId]: 0,
      }));

      return true;
    } catch (error) {
      console.error("Error marking conversation as read:", error);
      return false;
    }
  };

  // Connect to chat
  const connectToChat = useCallback(async () => {
    try {
      if (!userId || !token) {
        throw new Error("User ID and token are required");
      }

      setConnectionStatus("connecting");

      await chatClientRef.current.open({
        user: userId,
        accessToken: token,
      });

      await fetchPresenceInfo();
      await fetchUnreadCounts();
    } catch (error) {
      console.error("Connection error:", error);
      setError(error.message || "Failed to connect to chat service");
      setConnectionStatus("error");
    }
  }, [userId, token]);

  // Fetch presence information
  const fetchPresenceInfo = async (userIds = []) => {
    try {
      if (!chatClientRef.current || connectionStatus !== "connected") return;

      let targetUserIds = userIds;
      if (!targetUserIds.length) {
        try {
          const contacts = await chatClientRef.current.getRoster();
          console.log(".....contacts", contacts);
          targetUserIds = contacts
            .filter((contact) => contact.userId && contact.userId !== userId)
            .map((contact) => contact.userId);
        } catch (e) {
          console.warn("Failed to get roster:", e);
          return;
        }
      }

      if (!targetUserIds.length) return;

      const batchSize = 20;
      const batches = [];
      for (let i = 0; i < targetUserIds.length; i += batchSize) {
        batches.push(targetUserIds.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        try {
          batch.forEach((id) => presenceSubscribedUsers.current.add(id));

          await chatClientRef.current.subscribePresence({
            usernames: batch,
            expiry: 7 * 24 * 3600,
          });

          const presenceInfo = await chatClientRef.current.getPresenceStatus({
            usernames: batch,
          });

          if (presenceInfo) {
            setOnlineUsers((prev) => {
              const updated = new Set(prev);
              presenceInfo.forEach((p) => {
                if (p.status === "online") {
                  updated.add(p.userId);
                } else if (updated.has(p.userId)) {
                  updated.delete(p.userId);
                }
              });
              return updated;
            });
          }
        } catch (e) {
          console.error(`Error processing batch of users for presence:`, e);
        }
      }
    } catch (error) {
      console.error("Error fetching presence info:", error);
    }
  };

  // Fetch unread message counts
  const fetchUnreadCounts = async () => {
    try {
      if (!chatClientRef.current || connectionStatus !== "connected") return;

      const conversations = await chatClientRef.current.getConversationList();

      if (conversations && conversations.data) {
        const counts = {};
        conversations.data.forEach((conv) => {
          if (conv.unread > 0) {
            counts[conv.conversationId] = conv.unread;
          }
        });

        setUnreadCounts(counts);
      }
    } catch (error) {
      console.error("Error fetching unread counts:", error);
    }
  };

  // Load conversation with a specified user
  const loadConversation = useCallback(
    async (conversationId, cursor) => {
      try {
        if (!chatClientRef.current || !conversationId) {
          return { messages: [], cursor: null };
        }

        console.log(`Loading messages for conversation with ${conversationId}, cursor: ${cursor}`);

        const options = {
          chatType: "singleChat",
          targetId: conversationId,
          pageSize: 20,
        };

        if (cursor) {
          options.cursor = cursor;
        }

        // Fetch messages from the chat server
        const result = await chatClientRef.current.getHistoryMessages(options);

        console.log("History fetch result:", {
          messages: result.messages.length,
          cursor: result.cursor,
        });

        // Convert messages from Agora format to our app format
        const formattedMessages = [];
        for (const message of result.messages) {
          let formattedMessage;

          // Handle different message types
          if (message.type === 'txt') {
            // Regular text message
            formattedMessage = {
              id: message.id,
              text: message.msg,
              time: formatTime(new Date(message.time)),
              timestamp: message.time,
              from: message.from,
              to: message.to,
              status: "delivered",
              sent: message.from === userId,
            };
          } else if (message.type === 'cmd' && message.action === 'call_activity' && message.ext?.callActivity) {
            // Call activity message
            const callActivity = message.ext.callActivity;

            // Determine if this is from my perspective or the other person's
            const isFromMe = message.from === userId;
            const isOutgoing = isFromMe ? callActivity.isOutgoing : !callActivity.isOutgoing;

            // Only show important call activities
            let displayText = "";
            let shouldDisplayMessage = true;

            // Check if this is a video call
            const isVideoCall = callActivity.isVideoCall === true;
            const callIcon = isVideoCall ? "🎥" : "📞";

            switch(callActivity.type) {
              case "call_started":
                displayText = isOutgoing
                  ? `${callIcon} Outgoing ${isVideoCall ? 'video' : 'voice'} call`
                  : `${callIcon} Incoming ${isVideoCall ? 'video' : 'voice'} call`;
                break;
              case "call_missed":
                displayText = isOutgoing
                  ? `${callIcon} ${isVideoCall ? 'Video' : 'Voice'} call not answered`
                  : `${callIcon} Missed ${isVideoCall ? 'video' : 'voice'} call`;

                // Deduplicate missed call messages when loading conversation history
                if (callActivity.type === "call_missed" && !isOutgoing) {
                  const callId = callActivity.callId;
                  const peerId = message.from;

                  // Initialize set for this conversation if needed
                  if (!processedMissedCallsRef.current.has(peerId)) {
                    processedMissedCallsRef.current.set(peerId, new Set());
                  }

                  // Get the set of processed call IDs for this conversation
                  const processedCallIds = processedMissedCallsRef.current.get(peerId);

                  // Check if this call ID has already been processed
                  if (processedCallIds.has(callId)) {
                    // Skip this message as we've already processed it
                    shouldDisplayMessage = false;
                  } else {
                    // Mark as processed for future reference
                    processedCallIds.add(callId);
                  }
                }
                break;
              // Skip other call activities for now
              case "call_ended":
              case "call_rejected":
              case "call_answered":
              default:
                shouldDisplayMessage = false;
                break;
            }

            if (shouldDisplayMessage) {
              formattedMessage = {
                id: `call_activity_${message.id}`,
                text: displayText,
                time: formatTime(new Date(message.time)),
                timestamp: message.time,
                from: message.from,
                to: message.to,
                status: "delivered",
                sent: isFromMe,
                isSystemMessage: true,
                callActivity: {
                  ...callActivity,
                  // Update isOutgoing to match the perspective of the viewer
                  isOutgoing: isOutgoing
                },
              };
            }
          }

          if (formattedMessage) {
            formattedMessages.push(formattedMessage);
          }
        }

        // Sort messages by timestamp
        formattedMessages.sort((a, b) => a.timestamp - b.timestamp);

        // Update messages in state
        setMessages((prev) => {
          const existingMessages = prev[conversationId] || [];
          const existingIds = new Set(existingMessages.map((m) => m.id));
          const newMessages = formattedMessages.filter((m) => !existingIds.has(m.id));

          // Merge new messages with existing messages and sort by timestamp
          const mergedMessages = [...existingMessages, ...newMessages];
          mergedMessages.sort((a, b) => a.timestamp - b.timestamp);

          return {
            ...prev,
            [conversationId]: mergedMessages,
          };
        });

        return {
          messages: formattedMessages,
          cursor: result.cursor,
        };
      } catch (error) {
        console.error("Error loading conversation:", error);
        return { messages: [], cursor: null };
      }
    },
    [chatClientRef, userId]
  );

  // Send a message
  const sendMessage = useCallback(
    async (to, text) => {
      // Generate a temporary ID for optimistic UI update
      const tempId = `temp-${Date.now()}`;

      try {
        if (!chatClientRef.current || connectionStatus !== "connected") {
          throw new Error("Not connected to chat service");
        }

        const tempMessage = {
          id: tempId,
          text,
          time: formatTime(new Date()),
          timestamp: Date.now(),
          sent: true,
          from: userId,
          to,
          status: "sending",
        };

        setMessages((prev) => ({
          ...prev,
          [to]: [...(prev[to] || []), tempMessage],
        }));

        const options = {
          chatType: "singleChat",
          type: "txt",
          to: to,
          msg: text,
          ext: {
            userName: userId,
          },
        };

        const msg = AgoraChat.message.create(options);

        const originalMsgId = msg.id;

        pendingMessageIdsRef.current.set(tempId, originalMsgId);

        await chatClientRef.current.send(msg);

        setMessages((prev) => {
          const oldMessages = prev[to] || [];
          const updatedMessages = oldMessages.map((m) => {
            if (m.id === tempId) {
              return {
                ...m,
                id: originalMsgId,
                serverId: originalMsgId,
                status: "sent",
              };
            }
            return m;
          });

          return {
            ...prev,
            [to]: updatedMessages,
          };
        });

        return originalMsgId;
      } catch (error) {
        console.error("Error sending message:", error);

        setMessages((prev) => {
          const conversationMessages = [...(prev[to] || [])];
          const tempIndex = conversationMessages.findIndex(
            (m) => m.id === tempId
          );

          if (tempIndex !== -1) {
            conversationMessages[tempIndex] = {
              ...conversationMessages[tempIndex],
              status: "failed",
            };
          }

          return {
            ...prev,
            [to]: conversationMessages,
          };
        });

        throw error;
      }
    },
    [connectionStatus, userId]
  );

  // Resend failed message
  const resendMessage = useCallback(
    async (conversationId, messageId) => {
      try {
        if (!chatClientRef.current || connectionStatus !== "connected") {
          throw new Error("Not connected to chat service");
        }

        const conversationMessages = messages[conversationId] || [];
        const messageToResend = conversationMessages.find(
          (m) => m.id === messageId
        );

        if (!messageToResend) {
          throw new Error("Message not found");
        }

        setMessages((prev) => ({
          ...prev,
          [conversationId]: prev[conversationId].map((m) =>
            m.id === messageId ? { ...m, status: "sending" } : m
          ),
        }));

        const options = {
          chatType: "singleChat",
          type: "txt",
          to: conversationId,
          msg: messageToResend.text,
        };

        const msg = AgoraChat.message.create(options);
        const originalMsgId = msg.id;

        pendingMessageIdsRef.current.set(messageId, originalMsgId);

        await chatClientRef.current.send(msg);

        setMessages((prev) => ({
          ...prev,
          [conversationId]: prev[conversationId].map((m) =>
            m.id === messageId
              ? {
                  ...m,
                  id: originalMsgId,
                  serverId: originalMsgId,
                  status: "sent",
                }
              : m
          ),
        }));

        return originalMsgId;
      } catch (error) {
        console.error("Error resending message:", error);

        setMessages((prev) => ({
          ...prev,
          [conversationId]: prev[conversationId].map((m) =>
            m.id === messageId ? { ...m, status: "failed" } : m
          ),
        }));

        throw error;
      }
    },
    [connectionStatus, messages]
  );

  // Send typing indicator
  const sendTypingIndicator = useCallback(
    async (to, isTyping) => {
      try {
        if (!chatClientRef.current || connectionStatus !== "connected") return;

        const options = {
          chatType: "singleChat",
          type: "cmd",
          to: to,
          action: "typing",
          ext: {
            status: isTyping ? "typing" : "paused",
          },
        };

        const msg = AgoraChat.message.create(options);
        await chatClientRef.current.send(msg);
      } catch (error) {
        console.error("Error sending typing indicator:", error);
      }
    },
    [connectionStatus]
  );

  // Format time helper
  const formatTime = (date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    return `${hours % 12 || 12}:${minutes.toString().padStart(2, "0")} ${
      hours >= 12 ? "pm" : "am"
    }`;
  };

  // Publish custom presence state
  const publishPresence = useCallback(
    async (state) => {
      try {
        if (!chatClientRef.current || connectionStatus !== "connected")
          return false;

        await chatClientRef.current.publishPresence(state);
        return true;
      } catch (error) {
        console.error("Error publishing presence:", error);
        return false;
      }
    },
    [connectionStatus]
  );

  // Subscribe to presence for multiple users
  const subscribeToPresence = useCallback(
    async (userIds) => {
      try {
        if (
          !chatClientRef.current ||
          connectionStatus !== "connected" ||
          !userIds ||
          userIds.length === 0
        )
          return null;

        userIds.forEach((id) => presenceSubscribedUsers.current.add(id));

        const result = await chatClientRef.current.subscribePresence({
          usernames: userIds,
          expiry: 7 * 24 * 3600,
        });

        if (result && result.length > 0) {
          setOnlineUsers((prev) => {
            const updated = new Set(prev);
            result.forEach((presenceInfo) => {
              if (presenceInfo.status === "online") {
                updated.add(presenceInfo.userId);
              } else if (updated.has(presenceInfo.userId)) {
                updated.delete(presenceInfo.userId);
              }
            });
            return updated;
          });
        }

        return result;
      } catch (error) {
        console.error("Error subscribing to presence:", error);
        return null;
      }
    },
    [connectionStatus]
  );

  // Unsubscribe from presence for multiple users
  const unsubscribeFromPresence = useCallback(
    async (userIds) => {
      try {
        if (
          !chatClientRef.current ||
          connectionStatus !== "connected" ||
          !userIds ||
          userIds.length === 0
        )
          return false;

        userIds.forEach((id) => presenceSubscribedUsers.current.delete(id));

        await chatClientRef.current.unsubscribePresence({ usernames: userIds });

        setOnlineUsers((prev) => {
          const updated = new Set(prev);
          userIds.forEach((userId) => updated.delete(userId));
          return updated;
        });

        return true;
      } catch (error) {
        console.error("Error unsubscribing from presence:", error);
        return false;
      }
    },
    [connectionStatus]
  );

  // Get presence information for specific users
  const getPresenceInfo = useCallback(
    async (userIds) => {
      try {
        if (
          !chatClientRef.current ||
          connectionStatus !== "connected" ||
          !userIds ||
          userIds.length === 0
        )
          return [];

        const presenceInfo = await chatClientRef.current.getPresenceStatus({
          usernames: userIds,
        });

        if (presenceInfo && presenceInfo.length > 0) {
          setOnlineUsers((prev) => {
            const updated = new Set(prev);
            presenceInfo.forEach((info) => {
              if (info.status === "online") {
                updated.add(info.userId);
              } else if (updated.has(info.userId)) {
                updated.delete(info.userId);
              }
            });
            return updated;
          });
        }

        return presenceInfo;
      } catch (error) {
        console.error("Error getting presence info:", error);
        return [];
      }
    },
    [connectionStatus]
  );

  // Get list of subscribed presence users
  const getSubscribedPresenceUsers = useCallback(
    async (pageNum = 1, pageSize = 20) => {
      try {
        if (!chatClientRef.current || connectionStatus !== "connected")
          return null;

        const options = {
          pageNum,
          pageSize,
        };

        return await chatClientRef.current.getSubscribedPresenceList(options);
      } catch (error) {
        console.error("Error getting subscribed presence users:", error);
        return null;
      }
    },
    [connectionStatus]
  );

  // Add this function to useChat
  const renewToken = useCallback(async (newToken) => {
    try {
      if (!chatClientRef.current) return false;

      await chatClientRef.current.renewToken(newToken);
      return true;
    } catch (error) {
      console.error("Failed to renew token:", error);
      setError("Authentication failed. Please login again.");
      return false;
    }
  }, []);

  // Add isConnected helper function
  const isConnected = useCallback(() => {
    return connectionStatus === "connected";
  }, [connectionStatus]);

  const sendConversationReadReceipt = async (peerId) => {
    try {
      const options = {
        chatType: "singleChat",
        type: "channel",
        to: peerId,
      };
      const msg = AgoraChat.message.create(options);
      await chatClientRef.current.send(msg);
    } catch (error) {
      console.error("Error sending conversation read receipt:", error);
    }
  };

  // Send message-level read receipt (for a specific message)
  const sendMessageReadReceipt = async (peerId, messageId) => {
    try {
      const options = {
        type: "read",
        chatType: "singleChat",
        to: peerId,
        id: messageId,
      };
      const msg = AgoraChat.message.create(options);
      await chatClientRef.current.send(msg);
    } catch (error) {
      console.error("Error sending message read receipt:", error);
    }
  };

  // Send a call activity message to be saved in chat history
  const sendCallActivityMessage = useCallback(
    async (peerId, callActivity) => {
      try {
        if (!chatClientRef.current || !peerId || !callActivity) {
          console.error("Cannot send call activity: missing client, peerId, or callActivity data");
          return false;
        }

        console.log(`Sending call activity message to ${peerId}:`, callActivity);

        // Ensure callActivity has all required fields
        if (!callActivity.timestamp) {
          callActivity.timestamp = Date.now();
        }

        // Always send the message to the server for all activity types
        const options = {
          chatType: "singleChat",
          type: "cmd",
          to: peerId,
          action: "call_activity",
          ext: { callActivity }
        };

        // Create the message using the proper API
        const message = AgoraChat.message.create(options);

        // Send the message
        const result = await chatClientRef.current.send(message);
        console.log("Call activity message sent:", result);

        // For the UI, only show important call activities
        let displayText = "";
        let shouldDisplayMessage = true;
        let updateMissedCallCount = false;

        // Check if this is a video call
        const isVideoCall = callActivity.isVideoCall === true;
        const callIcon = isVideoCall ? "🎥" : "📞";

        switch(callActivity.type) {
          case "call_started":
            displayText = callActivity.isOutgoing
              ? `${callIcon} Outgoing ${isVideoCall ? 'video' : 'voice'} call`
              : `${callIcon} Incoming ${isVideoCall ? 'video' : 'voice'} call`;
            break;
          case "call_missed":
            if (callActivity.isOutgoing) {
              displayText = `${callIcon} ${isVideoCall ? 'Video' : 'Voice'} call not answered`;
            } else {
              displayText = `${callIcon} Missed ${isVideoCall ? 'video' : 'voice'} call`;
              // Only increment missed call count for incoming missed calls
              updateMissedCallCount = true;
            }
            break;
          // Skip other call activities for now
          case "call_ended":
          case "call_rejected":
          case "call_answered":
          default:
            shouldDisplayMessage = false;
            break;
        }

        // Only update UI for selected call activities
        if (shouldDisplayMessage) {
          const formattedMessage = {
            id: message.id,
            text: displayText,
            time: formatTime(new Date()),
            timestamp: Date.now(),
            from: userId,
            to: peerId,
            status: "sent",
            sent: true,
            isSystemMessage: true,
            callActivity: callActivity
          };

          setMessages((prev) => {
            const existingMessages = prev[peerId] || [];
            return {
              ...prev,
              [peerId]: [...existingMessages, formattedMessage],
            };
          });

          // Update missed call count if this is a missed call notification
          if (updateMissedCallCount && !callActivity.isOutgoing) {
            setMissedCallCounts((prev) => ({
              ...prev,
              [peerId]: (prev[peerId] || 0) + 1,
            }));
          }
        }

        return true;
      } catch (error) {
        console.error("Error sending call activity message:", error);
        return false;
      }
    },
    [chatClientRef, userId]
  );

  // Helper to format duration for call history (exported for use in other components)
  const formatCallDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;

    const mins = Math.floor(seconds / 60);
    const remainingSecs = seconds % 60;

    if (mins < 60) {
      return `${mins}m ${remainingSecs}s`;
    }

    const hours = Math.floor(mins / 60);
    const remainingMins = mins % 60;

    return `${hours}h ${remainingMins}m ${remainingSecs}s`;
  };

  // Add a function to get missed call count
  const getMissedCallCount = useCallback(
    (conversationId) => {
      return missedCallCounts[conversationId] || 0;
    },
    [missedCallCounts]
  );

  // Return the hook interface
  return {
    connectionStatus,
    messages,
    onlineUsers,
    typingUsers,
    unreadCounts,
    missedCallCounts,
    error,
    connectToChat,
    loadConversation,
    sendMessage,
    resendMessage,
    sendTypingIndicator,
    fetchPresenceInfo,
    getMessages: (conversationId) => messages[conversationId] || [],
    getUnreadCount: (conversationId) => unreadCounts[conversationId] || 0,
    getMissedCallCount,
    isOnline: (userId) => onlineUsers.has(userId),
    isTyping: (userId) => typingUsers.has(userId),
    clearError: () => setError(null),
    publishPresence,
    subscribeToPresence,
    unsubscribeFromPresence,
    getPresenceInfo,
    getSubscribedPresenceUsers,
    renewToken,
    isConnected,
    sendConversationReadReceipt,
    sendMessageReadReceipt,
    markConversationAsRead,
    disconnect: async () => {
      try {
        if (chatClientRef.current) {
          await chatClientRef.current.close();
        }
        return true;
      } catch (error) {
        console.error("Error disconnecting:", error);
        return false;
      }
    },
    sendCallActivityMessage,
    formatCallDuration, // Add the formatCallDuration function to the return object
  };
};

export default useChat;
