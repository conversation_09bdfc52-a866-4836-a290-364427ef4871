{"name": "cmtmeet3_web", "version": "0.1.0", "private": true, "dependencies": {"@fluentui/react": "^8.122.11", "@mui/material": "^7.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "agora-chat": "^1.3.1", "agora-rtc-react": "^2.4.0", "agora-rtc-sdk-ng": "^4.23.2", "agora-rtm-sdk": "^1.5.1", "axios": "^1.8.4", "bootstrap": "^5.3.3", "jquery": "^3.7.1", "lodash": "^4.17.21", "owl.carousel": "^2.3.4", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-owl-carousel2": "^0.3.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}