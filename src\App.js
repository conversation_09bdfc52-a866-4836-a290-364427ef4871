import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import Login from "./pages/login";
import Home from "./pages/home";
import Agenda from "./pages/agenda";
import CMTConnect from "./pages/cmtconnect";
import Networking from "./pages/networking";
import Watchlive from "./pages/watchlive";
import Playback from "./pages/playback";
import ProtectedRoute from "./components/ProtectedRoute";

import AgoraRTC, { AgoraRTCProvider } from "agora-rtc-react";
import VideoCallProvider from "./context/VideoCallContext";

// Enable logging in development mode
if (process.env.NODE_ENV === 'development') {
  AgoraRTC.setLogLevel(1); // 0 (none), 1 (error), 2 (warning), 3 (info), 4 (debug)
}

// Create Agora client instance with optimal audio configurations
const config = {
  mode: "rtc",
  codec: "vp8",
  enableAudioVolumeIndicator: true, // Enable volume indicators for audio
  audioOptimizationMode: "VoiceCall" // Optimize for voice calls
};

// Create client globally so it can be accessed by all components
const client = AgoraRTC.createClient(config);

function App() {
  return (
    <AgoraRTCProvider client={client}>
      <AuthProvider>
        <VideoCallProvider>
          <Router>
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />

              {/* Protected routes */}
              <Route path="/home" element={
                <ProtectedRoute>
                    <Home />
                </ProtectedRoute>
              } />

              <Route path="/agenda" element={
                <ProtectedRoute>
                    <Agenda />
                </ProtectedRoute>
              } />

              <Route path="/cmt-connect" element={
                <ProtectedRoute>
                    <CMTConnect />
                </ProtectedRoute>
              } />

              <Route path="/networking" element={
                <ProtectedRoute>
                    <Networking />
                </ProtectedRoute>
              } />

              <Route path="/watchlive" element={
                <ProtectedRoute>
                    <Watchlive />
                </ProtectedRoute>
              } />

              <Route path="/playback" element={
                <ProtectedRoute>
                    <Playback />
                </ProtectedRoute>
              } />

              {/* Default route - redirect to home if authenticated, login if not */}
              <Route path="/" element={
                <ProtectedRoute>
                  <Navigate to="/home" replace />
                </ProtectedRoute>
              } />

              {/* Catch all other routes */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </VideoCallProvider>
      </AuthProvider>
    </AgoraRTCProvider>
  );
}

export default App;